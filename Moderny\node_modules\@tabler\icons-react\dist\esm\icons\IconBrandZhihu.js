/**
 * @tabler/icons-react v2.47.0 - MIT
 */

import createReactComponent from '../createReactComponent.js';

var IconBrandZhihu = createReactComponent("brand-zhihu", "IconBrandZhihu", [
  ["path", { d: "M14 6h6v12h-2l-2 2l-1 -2h-1z", key: "svg-0" }],
  ["path", { d: "M4 12h6.5", key: "svg-1" }],
  ["path", { d: "M10.5 6h-5", key: "svg-2" }],
  ["path", { d: "M6 4c-.5 2.5 -1.5 3.5 -2.5 4.5", key: "svg-3" }],
  ["path", { d: "M8 6v7c0 4.5 -2 5.5 -4 7", key: "svg-4" }],
  ["path", { d: "M11 18l-3 -5", key: "svg-5" }]
]);

export { IconB<PERSON><PERSON>hihu as default };
//# sourceMappingURL=IconBrandZhihu.js.map
