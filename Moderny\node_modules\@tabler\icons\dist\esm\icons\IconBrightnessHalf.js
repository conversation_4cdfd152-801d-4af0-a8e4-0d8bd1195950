/**
 * @tabler/icons v2.47.0 - MIT
 */

var IconBrightnessHalf = (IconBrightnessHalf) => `<svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-brightness-half" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
  <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
  <path d="M12 9a3 3 0 0 0 0 6v-6z" />
  <path d="M6 6h3.5l2.5 -2.5l2.5 2.5h3.5v3.5l2.5 2.5l-2.5 2.5v3.5h-3.5l-2.5 2.5l-2.5 -2.5h-3.5v-3.5l-2.5 -2.5l2.5 -2.5z" />
</svg>`;

export { IconBrightnessHalf as default };
//# sourceMappingURL=IconBrightnessHalf.js.map
