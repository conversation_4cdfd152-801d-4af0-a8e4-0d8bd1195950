/**
 * @tabler/icons v2.47.0 - MIT
 */

var IconBrandXing = (IconBrandXing) => `<svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-brand-xing" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
  <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
  <path d="M16 21l-4 -7l6.5 -11" />
  <path d="M7 7l2 3.5l-3 4.5" />
</svg>`;

export { IconBrandXing as default };
//# sourceMappingURL=IconBrandXing.js.map
